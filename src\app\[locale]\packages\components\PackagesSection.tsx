import { getTranslations } from "next-intl/server";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import PackageCarousel from "./PackageCarousel";
import type { PackageItem } from "./PackageCarousel";
import { BadgePlus } from "lucide-react";
import ImgLogo from "@/assets/imgs/package-logo.png";
import ImgDesign from "@/assets/imgs/package-design.png";
import ImgWeb from "@/assets/imgs/package-web_essencial.png";
import ImgMediaAnimada from "@/assets/imgs/package-media_animada.png";
import ImgMediaPost from "@/assets/imgs/package-media_post.png";

type Props = {
  className?: string;
};

const PackagesSection = async ({ className }: Props) => {
  const t = await getTranslations("packages");
  const tCommon = await getTranslations("common");

  const personalPackages: PackageItem[] = [
    {
      id: "logo",
      title: t("items.logo.title"),
      description: t("items.logo.summary"),
      image: ImgLogo,
      features: t.raw("items.logo.features") as string[],
      ctaLabel: tCommon("request"),
    },
    {
      id: "design",
      title: t("items.design.title"),
      description: t("items.design.summary"),
      image: ImgDesign,
      features: t.raw("items.design.features") as string[],
      ctaLabel: tCommon("request"),
    },
    {
      id: "web",
      title: t("items.web.title"),
      description: t("items.web.summary"),
      image: ImgWeb,
      features: t.raw("items.web.features") as string[],
      ctaLabel: tCommon("request"),
    },
    {
      id: "media-animada",
      title: t("items.media_animada.title"),
      description: t("items.media_animada.summary"),
      image: ImgMediaAnimada,
      features: t.raw("items.media_animada.features") as string[],
      ctaLabel: tCommon("request"),
    },
    {
      id: "media-postagem",
      title: t("items.media_postagem.title"),
      description: t("items.media_postagem.summary"),
      image: ImgMediaPost,
      features: t.raw("items.media_postagem.features") as string[],
      ctaLabel: tCommon("request"),
    },
  ];

  return (
    <section id="packages" className={cn("flex flex-col gap-10", className)}>
      <span className="mx-auto flex max-w-4xl flex-col gap-10 text-center text-balance">
        <h3 className="text-foreground text-4xl font-bold sm:text-5xl">
          {t("title")}
        </h3>
        <p className="text-muted-foreground text-lg sm:text-xl">
          {t("description")}
        </p>
      </span>

      <Tabs defaultValue="personal" className="mx-auto flex flex-col gap-10">
        <TabsList className="mx-auto grid w-fit grid-cols-2">
          <TabsTrigger value="personal">{t("tabs.personal")}</TabsTrigger>
          <TabsTrigger value="business">{t("tabs.business")}</TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="">
          <PackageCarousel items={personalPackages} />
        </TabsContent>

        <TabsContent value="business" className="">
          <div className="bg-neutral text-muted-foreground mx-auto max-w-3xl rounded-lg border p-8 text-center">
            <BadgePlus className="mx-auto mb-3 h-8 w-8" />
            <p>{t("businessSoon")}</p>
          </div>
        </TabsContent>
      </Tabs>
    </section>
  );
};

export default PackagesSection;
