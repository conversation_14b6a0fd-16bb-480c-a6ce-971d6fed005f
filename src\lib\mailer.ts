import nodemailer from "nodemailer";
import { appEnv } from "@/env";
export type EmailData = {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
};
const transporter = nodemailer.createTransport({
  host: appEnv.SMTP_HOST,
  port: appEnv.SMTP_PORT,
  secure: appEnv.SMTP_PORT === 465,
  auth: { user: appEnv.SMTP_USER, pass: appEnv.SMTP_PASSWORD },
});

export const sendEmail = async ({ to, subject, text, html }: EmailData) => {
  const info = await transporter.sendMail({
    from: appEnv.SMTP_FROM,
    to: Array.isArray(to) ? to.join(", ") : to,
    subject,
    text,
    html,
  });

  if (appEnv.NODE_ENV === "development") {
    console.log("Preview URL:", nodemailer.getTestMessageUrl(info));
  }
};
