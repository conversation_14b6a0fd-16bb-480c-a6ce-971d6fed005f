/* Custom PhotoSwipe styles */
.pswp {
  --pswp-bg: rgba(0, 0, 0, 0.85);
}

.pswp__button {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.pswp__button:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.pswp__counter {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px 8px;
}

/* Hide scrollbar for thumbnail carousel */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
