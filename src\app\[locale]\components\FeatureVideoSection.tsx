"use client";

import { useState, useRef } from "react";
import { Play } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const FeatureVideoSection = ({ className }: Props) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlay = () => {
    if (videoRef.current) {
      videoRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
  };

  return (
    <section
      className={cn(
        "grid-stack relative grid min-h-[60vh] place-items-center",
        className,
      )}
    >
      <div className="stack-item bg-primary/30 -mt-4 h-[50%] w-[80%] self-start rounded-full [box-shadow:0_-4px_64px_0_rgb(from_var(--color-primary)_r_g_b_/.3)] blur-[64px]" />
      <video
        ref={videoRef}
        className="stack-item z-0 mx-auto h-auto w-full rounded-md"
        onEnded={handleVideoEnded}
        controls={isPlaying}
        poster=""
      >
        <source src="/vids/feature-video.webm" type="video/webm" />
        Your browser does not support the video tag.
      </video>

      {!isPlaying && (
        <button
          onClick={handlePlay}
          className="bg-foreground/50 stack-item hocus:bg-foreground/70 hocus:scale-105 z-10 grid aspect-square h-fit w-fit cursor-pointer place-items-center rounded-full p-6 duration-300 ease-out"
          aria-label="Play video"
        >
          <Play className="ml-1 size-12 fill-black text-black" />
        </button>
      )}
      <div
        className={cn(
          "stack-item from-background pointer-events-none z-0 h-full w-full bg-gradient-to-t duration-300 ease-out",
          isPlaying ? "opacity-0" : "opacity-100",
        )}
      />
    </section>
  );
};

export default FeatureVideoSection;
