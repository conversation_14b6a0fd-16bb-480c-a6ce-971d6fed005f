import { getTranslations } from "next-intl/server";
import { cn } from "@/lib/utils";
import ContactForm from "./ContactForm";
import ContactInfo from "./ContactInfo";

type Props = {
  className?: string;
};

const ContactSection = async ({ className }: Props) => {
  const t = await getTranslations("contact");

  return (
    <section
      id="contact"
      className={cn("bg-background flex flex-col gap-16", className)}
    >
      {/* Header */}
      <header className="text-center">
        <h2 className="text-foreground mb-6 text-4xl font-bold lg:text-5xl">
          {t("title")}
        </h2>
        <p className="text-muted-foreground mx-auto max-w-3xl text-lg lg:text-xl">
          {t("description")}
        </p>
      </header>

      {/* Content */}
      <div className="mx-auto grid w-full max-w-5xl items-start gap-10 lg:grid-cols-2">
        <ContactInfo />
        <ContactForm />
      </div>
    </section>
  );
};

export default ContactSection;
