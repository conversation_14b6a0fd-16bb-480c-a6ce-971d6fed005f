"use client";

import { cn } from "@/lib/utils";
import { useState } from "react";

import ClientApetrechos from "@/assets/imgs/client-client-apetrechos.svg";
import ClientAzeviche from "@/assets/imgs/client-client-azeviche.svg";
import ClientEmv from "@/assets/imgs/client-client-emv.svg";
import ClientEnbusiness from "@/assets/imgs/client-client-enbusiness.svg";
import ClientEpemarTrading from "@/assets/imgs/client-client-epemar_trading.svg";
import ClientFrecelCh from "@/assets/imgs/client-client-frecel_ch.svg";
import ClientLinhaETecido from "@/assets/imgs/client-client-linha_e_tecido.svg";
import ClientNowa from "@/assets/imgs/client-client-nowa.svg";

type Props = {
  className?: string;
};

const clientLogos = [
  { name: "Apetrechos", component: ClientApetrechos },
  { name: "<PERSON><PERSON><PERSON><PERSON>", component: Client<PERSON><PERSON>vich<PERSON> },
  { name: "EMV", component: ClientEmv },
  { name: "Enbusiness", component: ClientEnbusiness },
  { name: "Epemar Trading", component: ClientEpemarTrading },
  { name: "Frecel CH", component: ClientFrecelCh },
  { name: "Linha e Tecido", component: ClientLinhaETecido },
  { name: "Nowa", component: ClientNowa },
];

const ClientsList = ({ className }: Props) => {
  const [isPaused, setIsPaused] = useState(false);

  const duplicatedLogos = [...clientLogos, ...clientLogos];

  return (
    <section className={cn("w-full overflow-hidden", className)}>
      <ul
        className={cn(
          "flex gap-10 motion-safe:animate-[marquee_30s_linear_infinite] sm:gap-20",
          isPaused && "motion-safe:[animation-play-state:paused]",
        )}
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
      >
        {duplicatedLogos.map((logo, index) => {
          const LogoComponent = logo.component;
          return (
            <li
              key={`${logo.name}-${index}`}
              className={cn(
                "group flex h-24 w-36 flex-shrink-0 items-center justify-center rounded-md bg-white p-2.5 opacity-40 mix-blend-luminosity transition-all duration-300 ease-in-out hover:opacity-100",
              )}
            >
              <LogoComponent
                className="max-h-full max-w-full object-contain mix-blend-luminosity group-hover:mix-blend-normal"
                alt={`${logo.name} logo`}
              />
            </li>
          );
        })}
      </ul>

      <style jsx>{`
        @keyframes marquee {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
      `}</style>
    </section>
  );
};

export default ClientsList;
