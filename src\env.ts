import * as z from "zod/v4-mini";

const envSchema = z.object({
  GOOGLE_MAPS_API_KEY: z.string(),
  SMTP_HOST: z.string(),
  SMTP_PORT: z.coerce.number(),
  SMTP_USER: z.string(),
  SMTP_PASSWORD: z.string(),
  SMTP_FROM: z.string(),
  NODE_ENV: z.enum(["development", "production", "test"]),
  CONTACT_EMAIL: z.string(),
});

const result = envSchema.safeParse(process.env);

if (!result.success) {
  console.error(
    "❌ Invalid environment variables:",
    z.prettifyError(result.error),
  );
  process.exit(1);
}

export const appEnv = result.data;
