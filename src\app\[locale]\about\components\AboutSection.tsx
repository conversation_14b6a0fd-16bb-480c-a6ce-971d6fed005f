import { getTranslations } from "next-intl/server";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs";
import DocumentIcon from "@/assets/imgs/document.svg";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const AboutSection = async ({ className }: Props) => {
  const t = await getTranslations("about");
  const aboutKeys = ["about", "mission", "vision", "values"] as const;

  return (
    <section className={cn("", className)}>
      <Tabs
        defaultValue="about"
        className="bg-neutral mx-auto max-w-5xl gap-10 rounded-lg border p-10"
      >
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 lg:w-fit">
          {aboutKeys.map((key) => (
            <TabsTrigger key={key} value={key}>
              {t(`${key}.label`)}
            </TabsT<PERSON>ger>
          ))}
        </TabsList>

        {aboutKeys.map((key) => (
          <TabsContent
            key={key}
            value={key}
            className="grid grid-rows-[auto_1fr] gap-x-10 gap-y-5 sm:grid-cols-[1fr_auto]"
          >
            <h2 className="text-foreground text-4xl font-bold sm:text-5xl">
              {t(`${key}.title`)}
            </h2>
            <p className="text-muted-foreground text-lg sm:text-xl">
              {t(`${key}.description`)}
            </p>

            <div className="row-span-2 row-start-1 flex-shrink-0 sm:col-start-2">
              <DocumentIcon className="text-primary h-48 w-auto lg:h-56" />
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </section>
  );
};

export default AboutSection;
