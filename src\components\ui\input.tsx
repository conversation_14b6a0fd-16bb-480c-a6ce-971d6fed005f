import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground border-border bg-card flex w-full min-w-0 rounded-sm border px-2.5 py-1.5 text-sm shadow-xs ring-2 ring-transparent duration-300 ease-out outline-none file:inline-flex file:bg-transparent file:text-sm file:font-medium placeholder:text-base disabled:cursor-not-allowed disabled:opacity-50 sm:text-base",
        "focus-visible:border-ring",
        "focus-visible:ring-ring/50",
        "aria-invalid:border-destructive",
        className,
      )}
      {...props}
    />
  );
}

export { Input };
