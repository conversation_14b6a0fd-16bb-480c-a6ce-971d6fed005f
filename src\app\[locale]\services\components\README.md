# ServicePreviewDialog Component

## Overview
The ServicePreviewDialog component provides a modal dialog for previewing service items with image galleries and PhotoSwipe integration.

## Features
- **Modal Dialog**: Uses shadcn/ui Dialog component with custom styling
- **Image Gallery**: Displays main image with thumbnail carousel navigation
- **PhotoSwipe Integration**: Click main image to open full-screen gallery
- **URL State Management**: Uses nuqs for server-side search params handling
- **Responsive Design**: Adapts to different screen sizes
- **Internationalization**: Supports multiple languages via next-intl

## Usage

### Basic Implementation
```tsx
import ServicePreviewDialog from './ServicePreviewDialog';

// In your component
<ServicePreviewDialog 
  services={serviceItems} 
  previewKey={previewKey} 
/>
```

### Service Item Structure
```tsx
type ServiceItem = {
  key: string;           // Unique identifier
  category: string;      // Service category
  image: StaticImageData; // Main image
  images?: StaticImageData[]; // Additional images for carousel
  title: string;         // Service title
  description: string;   // Service description
};
```

### Opening the Dialog
The dialog opens when the `previewKey` prop matches a service's `key`. This is typically managed through URL search parameters:

```tsx
// To open dialog for service with key "frecel"
// URL: /?preview=frecel
```

### Search Params Configuration
The search params are configured in `searchParams.ts`:

```tsx
export const serviceSearchParams = {
  preview: parseAsString.withDefault(""),
};
```

## PhotoSwipe Integration
- Click the main image to open PhotoSwipe gallery
- Supports zoom, pan, and navigation
- Custom CSS styling in `photoswipe.css`
- Automatically handles image dimensions

## Styling
- Uses Tailwind CSS for responsive design
- Custom PhotoSwipe styles for better integration
- Supports dark/light theme modes
- Scrollbar hidden for thumbnail carousel

## Dependencies
- `photoswipe`: Image gallery library
- `nuqs`: URL state management
- `next-intl`: Internationalization
- `shadcn/ui`: UI components
