import { LocaleType, routing } from "@/i18n/routing";
import { setRequestLocale } from "next-intl/server";
import HeroSection from "./components/HeroSection";
import FeatureVideoSection from "./components/FeatureVideoSection";
import ClientsList from "./components/ClientsList";
import AboutSection from "./about/components/AboutSection";
import ServicesSection from "./services/components/ServicesSection";
import PackagesSection from "./packages/components/PackagesSection";
import PartnershipSection from "./partnership/components/PartnershipSection";
import ContactSection from "./contact/ContactSection";

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

const HomePage = async ({ params, searchParams }: PageProps<"/[locale]">) => {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale as LocaleType);

  return (
    <main className="">
      <HeroSection className="container-full" />
      <FeatureVideoSection className="container-full" />
      <ClientsList className="container-full" />
      <AboutSection className="container-full" />
      <ServicesSection className="container-full" />
      <PackagesSection className="container-full" />
      <PartnershipSection className="container-full" />
      <ContactSection className="container-full" />
    </main>
  );
};

export default HomePage;
