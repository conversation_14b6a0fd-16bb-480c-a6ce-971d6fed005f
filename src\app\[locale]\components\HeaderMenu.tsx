"use client";

import { Menu } from "lucide-react";
import { useTranslations } from "next-intl";
import { Link } from "@/i18n/navigation";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import LanguageSelector from "./LanguageSelector";
import ThemeSelector from "./ThemeSelector";
import { navLinks, links } from "../links";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const HeaderMenu = ({ className }: Props) => {
  const tNav = useTranslations("navigation");

  return (
    <div className={cn(className)}>
      <DropdownMenu>
        <DropdownMenuTrigger
          className={cn(buttonVariants({ variant: "ghost", size: "icon" }))}
        >
          <Menu className="h-5 w-5" />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="flex w-60 flex-col gap-5 p-5"
        >
          <nav>
            <ul className="flex flex-col gap-2.5">
              {Object.entries(navLinks).map(([key, link]) => (
                <li key={key}>
                  <Link
                    className={buttonVariants({ variant: "link" })}
                    href={link.href}
                  >
                    {tNav(link.key)}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
          <DropdownMenuSeparator />
          <span className="flex flex-wrap items-center gap-5">
            <ThemeSelector />
            <LanguageSelector />
            <Button variant="secondary" className="text-premium">
              {tNav(links.setWA.key)}
            </Button>
            <Button variant="secondary">{tNav(links.signOn.key)}</Button>
          </span>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default HeaderMenu;
