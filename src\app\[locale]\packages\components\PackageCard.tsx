import Image, { type StaticImageData } from "next/image";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Check, Info, Sparkle } from "lucide-react";

export type PackageCardProps = {
  className?: string;
  title: string;
  description: string;
  image: StaticImageData;
  features: string[];
  ctaLabel?: string;
};

const PackageCard = ({
  className,
  title,
  description,
  image,
  features = [],
  ctaLabel = "Solicitar",
}: PackageCardProps) => {
  return (
    <article
      className={cn(
        "group border-border flex h-full flex-col overflow-hidden rounded-md border",
        className,
      )}
    >
      <div className="bg-neutral flex flex-col items-center justify-center gap-5 p-5 pt-10">
        <picture className="flex aspect-square h-36 items-center justify-center">
          <Image
            src={image}
            alt={title}
            className="h-full w-full object-contain"
          />
        </picture>
        <h3 className="text-foreground text-xl font-bold tracking-tight sm:text-2xl">
          {title}
        </h3>
        <span className="flex w-full gap-2.5">
          <Button variant="secondary" size="sm" className="text-primary flex-1">
            {ctaLabel}
          </Button>
          <Button variant="secondary" size="sm" className="w-fit">
            <Info className="size-4" strokeWidth={1.3} />
          </Button>
        </span>
      </div>

      <div className="text-muted-foreground flex flex-col gap-5 p-5">
        <p className="">{description}</p>
        <hr className="border-border" />
        <ul className="flex flex-col gap-2.5">
          {features.map((feat, idx) => (
            <li key={idx} className="flex items-start gap-2">
              <Sparkle
                strokeWidth={1.3}
                className="text-premium mt-[0.15rem] size-4 flex-shrink-0"
              />
              <p>{feat}</p>
            </li>
          ))}
        </ul>
      </div>
    </article>
  );
};

export default PackageCard;
