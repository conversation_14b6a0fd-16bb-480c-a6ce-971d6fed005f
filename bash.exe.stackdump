Stack trace:
Frame         Function      Args
0007FFFF9FA0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8EA0) msys-2.0.dll+0x1FE8E
0007FFFF9FA0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA278) msys-2.0.dll+0x67F9
0007FFFF9FA0  000210046832 (000210286019, 0007FFFF9E58, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9FA0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9FA0  000210068E24 (0007FFFF9FB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA280  00021006A225 (0007FFFF9FB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC83EA0000 ntdll.dll
7FFC82B40000 KERNEL32.DLL
7FFC81870000 KERNELBASE.dll
7FFC83010000 USER32.dll
7FFC81030000 win32u.dll
7FFC83C50000 GDI32.dll
7FFC81400000 gdi32full.dll
7FFC81060000 msvcp_win.dll
7FFC81530000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC83230000 advapi32.dll
7FFC81E20000 msvcrt.dll
7FFC82180000 sechost.dll
7FFC82780000 RPCRT4.dll
7FFC80670000 CRYPTBASE.DLL
7FFC81680000 bcryptPrimitives.dll
7FFC81FE0000 IMM32.DLL
