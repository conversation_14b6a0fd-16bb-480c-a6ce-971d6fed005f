import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { <PERSON>, Rocket, Sparkle } from "lucide-react";
import BannerImg from "@/assets/imgs/partnership-banner.jpg";

type Props = {
  className?: string;
};

const PartnershipSection = async ({ className }: Props) => {
  return (
    <section className="grid-stack relative isolate overflow-hidden">
      <Image
        src={BannerImg}
        alt="Parcerias banner"
        priority
        fill
        className="object-cover"
      />
      <div className="from-primary/60 via-background/80 to-background absolute inset-0 bg-gradient-to-b via-75%" />
      <article
        className={cn("relative flex flex-col gap-10 text-center", className)}
      >
        <h2 className="text-neutral mx-auto max-w-3xl text-3xl leading-tight font-bold text-shadow-md sm:text-5xl">
          Junte-se a nós e faça parte da experiência
        </h2>

        <span className="flex flex-wrap items-center justify-center gap-5">
          <Button className="gap-2">
            <Rocket className="size-4" />
            Começar
          </Button>
          <Button variant="secondary" className="gap-2">
            <Play className="size-4" />
            Ver Vídeo
          </Button>
        </span>

        <ul className="text-muted-foreground flex flex-wrap items-center justify-center gap-x-6 gap-y-2 text-sm">
          {[
            "Projetos Exclusivos",
            "Networking Premium",
            "Receita Compartilhada",
            "Suporte Especializado",
          ].map((label) => (
            <li key={label} className="inline-flex items-center gap-2">
              <Sparkle className="text-premium size-4" strokeWidth={1.3} />
              <span>{label}</span>
            </li>
          ))}
        </ul>
      </article>
    </section>
  );
};

export default PartnershipSection;
