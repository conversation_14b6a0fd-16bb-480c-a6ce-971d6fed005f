import { StaticImageData } from "next/image";

// Service images
import ServiceFenixDataImg from "@/assets/imgs/service-design-fenix-data.jpg";
import ServiceHellenHomeImg from "@/assets/imgs/service-design-hellen_home.jpg";
import ServiceLinhaTecidoImg from "@/assets/imgs/service-design-linha_e_tecido.jpg";
import ServiceSoloImg from "@/assets/imgs/service-design-solo.jpg";
import ServiceAndiamoImg from "@/assets/imgs/service-programming-andiamo.jpg";
import ServiceAzulImg from "@/assets/imgs/service-programming-azul.jpg";
import ServiceFrecelImg from "@/assets/imgs/service-design-frecel.jpg";

export type ServiceCategoryType =
  | "all"
  | "animation"
  | "design"
  | "programming"
  | "marketing"
  | "audiovisual"
  | "3d";

export const serviceCategories: ServiceCategoryType[] = [
  "all",
  "animation",
  "design",
  "programming",
  "marketing",
  "audiovisual",
  "3d",
];

export const servicesData = [
  {
    key: "fenix",
    category: "design",
    images: [ServiceFenixDataImg],
    titleKey: "items.fenix.title",
    descriptionKey: "items.fenix.description",
  },
  {
    key: "hellen",
    category: "design",
    images: [ServiceHellenHomeImg],
    titleKey: "items.hellen.title",
    descriptionKey: "items.hellen.description",
  },
  {
    key: "linha_tecido",
    category: "design",
    images: [ServiceLinhaTecidoImg],
    titleKey: "items.linha_tecido.title",
    descriptionKey: "items.linha_tecido.description",
  },
  {
    key: "solo",
    category: "design",
    images: [
      ServiceSoloImg,
      ServiceSoloImg, // for testing
      ServiceSoloImg, // for testing
    ],
    titleKey: "items.solo.title",
    descriptionKey: "items.solo.description",
  },
  {
    key: "andiamo",
    category: "programming",
    images: [ServiceAndiamoImg],
    titleKey: "items.andiamo.title",
    descriptionKey: "items.andiamo.description",
  },
  {
    key: "azul",
    category: "programming",
    images: [ServiceAzulImg],
    titleKey: "items.azul.title",
    descriptionKey: "items.azul.description",
  },
  {
    key: "frecel",
    category: "design",
    images: [
      ServiceFrecelImg,
      ServiceFrecelImg, // for testing
      ServiceFrecelImg, // for testing
    ],
    titleKey: "items.frecel.title",
    descriptionKey: "items.frecel.description",
  },
] as const;

export type ServiceItemType = (typeof servicesData)[number];
