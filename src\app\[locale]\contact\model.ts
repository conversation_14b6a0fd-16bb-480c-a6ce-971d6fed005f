import { useTranslations } from "next-intl";
import * as z from "zod/v4-mini";

export const contactFormSchema = ({
  tValidation,
  tCommon,
}: {
  tValidation: ReturnType<typeof useTranslations<"validation">>;
  tCommon: ReturnType<typeof useTranslations<"common">>;
}) =>
  z.object({
    firstName: z.string().check(
      z.minLength(
        2,
        tValidation("minLengthError", {
          field: tC<PERSON>mon("firstName"),
          min: "2",
        }),
      ),
      z.maxLength(
        50,
        tValidation("maxLengthError", {
          field: tCommon("firstName"),
          max: "50",
        }),
      ),
    ),
    lastName: z.string().check(
      z.minLength(
        2,
        tValidation("minLengthError", {
          field: tCommon("lastName"),
          min: "2",
        }),
      ),
      z.maxLength(
        50,
        tValidation("maxLengthError", {
          field: tCom<PERSON>("lastName"),
          max: "50",
        }),
      ),
    ),
    email: z.email(tValidation("emailError")),
    phone: z.string().check(
      z.regex(
        /^\+\d{1,3}\d{9}$/,
        tValidation("regexError", {
          field: tCommon("phone"),
          format: "+(area)(9 digits)",
        }),
      ),
    ),
    subject: z
      .string()
      .check(
        z.minLength(
          1,
          tValidation("minLengthError", {
            field: tCommon("subject"),
            min: "1",
          }),
        ),
      ),
    message: z.string().check(
      z.minLength(
        10,
        tValidation("minLengthError", {
          field: tCommon("message"),
          min: "10",
        }),
      ),
      z.maxLength(
        1000,
        tValidation("maxLengthError", {
          field: tCommon("message"),
          max: "1000",
        }),
      ),
    ),
  });
export type ContactFormData = z.infer<ReturnType<typeof contactFormSchema>>;
