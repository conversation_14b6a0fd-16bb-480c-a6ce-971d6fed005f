@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));
@custom-variant hocus (&:hover, &:focus);

:root {
  --neutral: hsl(0 0% 100%);

  --background: hsl(0 6.6667% 97.0588%);
  --foreground: hsl(45 3.6364% 21.5686%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(45 3.6364% 21.5686%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(45 3.6364% 21.5686%);
  --primary: hsl(14.9282 96.3134% 57.451%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(14.9282 96.3134% 57.451%);
  --secondary-foreground: hsl(0 0% 100%);
  --muted: hsl(60 4% 95.098%);
  --muted-foreground: hsl(45 3.7383% 41.9608%);
  --accent: hsl(60 4% 95.098%);
  --accent-foreground: hsl(14.9282 96.3134% 57.451%);
  --destructive: hsl(0 100% 37.0588%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 81.9608%);
  --input: hsl(0 0% 100%);
  --ring: hsl(0 0.6993% 71.9608%);
  --chart-1: hsl(14.3662 100% 86.0784%);
  --chart-2: hsl(14.8718 100% 77.0588%);
  --chart-3: hsl(14.902 100% 70%);
  --chart-4: hsl(14.8315 100% 65.098%);
  --chart-5: hsl(15.0685 100% 57.0588%);
  --sidebar: hsl(0 0% 100%);
  --sidebar-foreground: hsl(45 3.6364% 21.5686%);
  --sidebar-primary: hsl(14.9282 96.3134% 57.451%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(60 4% 95.098%);
  --sidebar-accent-foreground: hsl(14.9282 96.3134% 57.451%);
  --sidebar-border: hsl(0 0% 81.9608%);
  --sidebar-ring: hsl(0 0.6993% 71.9608%);

  --radius: 0.625rem;
  --shadow-2xs: 0 1px 2px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 2px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 2px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --neutral: hsl(48, 4%, 23%);

  --background: hsl(48 3.937% 24.902%);
  --foreground: hsl(0 6.6667% 97.0588%);
  --card: hsl(48 3.937% 24.902%);
  --card-foreground: hsl(0 6.6667% 97.0588%);
  --popover: hsl(48 3.937% 24.902%);
  --popover-foreground: hsl(0 6.6667% 97.0588%);
  --primary: hsl(14.9282 96.3134% 57.451%);
  --primary-foreground: hsl(0 6.6667% 97.0588%);
  --secondary: hsl(14.9282 96.3134% 57.451%);
  --secondary-foreground: hsl(0 6.6667% 97.0588%);
  --muted: hsl(45 3.9216% 20%);
  --muted-foreground: hsl(48 3.4965% 71.9608%);
  --accent: hsl(45 3.9216% 20%);
  --accent-foreground: hsl(14.9282 96.3134% 57.451%);
  --destructive: hsl(0 100% 37.0588%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 32.1569%);
  --input: hsl(48 4.2735% 22.9412%);
  --ring: hsl(0 0% 41.1765%);
  --chart-1: hsl(14.3662 100% 86.0784%);
  --chart-2: hsl(14.8718 100% 77.0588%);
  --chart-3: hsl(14.902 100% 70%);
  --chart-4: hsl(14.8315 100% 65.098%);
  --chart-5: hsl(15.0685 100% 57.0588%);
  --sidebar: hsl(48 4.2735% 22.9412%);
  --sidebar-foreground: hsl(0 6.6667% 97.0588%);
  --sidebar-primary: hsl(14.9282 96.3134% 57.451%);
  --sidebar-primary-foreground: hsl(0 6.6667% 97.0588%);
  --sidebar-accent: hsl(45 3.9216% 20%);
  --sidebar-accent-foreground: hsl(14.9282 96.3134% 57.451%);
  --sidebar-border: hsl(0 0% 32.1569%);
  --sidebar-ring: hsl(0 0% 41.1765%);

  --radius: 0.625rem;
  --shadow-2xs: 0 1px 2px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 2px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 2px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 2px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-premium: hsl(41, 61%, 48%);
  --color-neutral: var(--neutral);

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-nunito-sans);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --breakpoint-mdx: 50rem;
}

@layer base {
  * {
    @apply border-border outline-ring/50 font-sans tracking-wide;
  }
  body,
  html {
    @apply bg-background text-foreground min-h-screen scroll-smooth;
  }
}

@utility container-fill {
  @apply mx-auto w-full px-5 sm:px-20;
}

@utility container-x {
  @apply container-fill max-w-[90rem];
}

@utility container-full {
  @apply container-x py-[5rem] sm:py-[7.5rem];
}

@utility grid-stack {
  @apply [grid-template-areas:'stack'];
}

@utility stack-item {
  @apply [grid-area:stack];
}
