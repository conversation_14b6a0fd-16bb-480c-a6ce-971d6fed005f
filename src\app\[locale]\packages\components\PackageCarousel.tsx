"use client";

import { cn } from "@/lib/utils";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import PackageCard, { type PackageCardProps } from "./PackageCard";

export type PackageItem = PackageCardProps & { id: string };

type Props = {
  className?: string;
  items: PackageItem[];
};

const PackageCarousel = ({ className, items }: Props) => {
  return (
    <Carousel
      opts={{ align: "center" }}
      className={cn("relative grid", className)}
    >
      <CarouselContent className="-ml-5">
        {items.map((item) => (
          <CarouselItem
            key={item.id}
            className="max-w-96 basis-full pl-5 md:basis-1/2 lg:basis-1/3 xl:basis-1/4"
          >
            <PackageCard {...item} />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className="hidden sm:flex" />
      <CarouselNext className="hidden sm:flex" />
    </Carousel>
  );
};

export default PackageCarousel;
