"use client";

import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";
import { ContactFormData, contactFormSchema } from "./model";
import { useTransition } from "react";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";

type Props = {
  className?: string;
};

const ContactForm = ({ className }: Props) => {
  const t = useTranslations("contact.form");
  const tValidation = useTranslations("validation");
  const tCommon = useTranslations("common");
  const [isPending, startTransition] = useTransition();

  const form = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema({ tValidation, tCommon })),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
    },
  });

  const onSubmit = async (_data: ContactFormData) =>
    startTransition(async () => {
      // const result = await sendContactEmail(data);
      const result = { success: true }; // TODO: Just for testing

      if (!result.success) {
        toast.error(t("error"));
        return;
      }

      toast.success(t("success"));
      form.reset({});
    });
  const tFormSubjects = useTranslations("contact.form.subjects");

  const subjectOptions = [
    { value: "design", label: tFormSubjects("design") },
    { value: "animation", label: tFormSubjects("animation") },
    { value: "programming", label: tFormSubjects("programming") },
    { value: "marketing", label: tFormSubjects("marketing") },
    { value: "support", label: tFormSubjects("support") },
    { value: "audiovisual", label: tFormSubjects("audiovisual") },
    { value: "3d", label: tFormSubjects("3d") },
    { value: "finance", label: tFormSubjects("finance") },
  ];

  return (
    <article className={cn("bg-card w-full rounded-lg border p-5", className)}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-6">
          {/* First Name and Last Name Row */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tCommon("firstName")}</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Denilson" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{tCommon("lastName")}</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Costa" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Email */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tCommon("email")}</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    placeholder="<EMAIL>"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Phone */}
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tCommon("phone")}</FormLabel>
                <FormControl>
                  <Input {...field} type="tel" placeholder="+244958707556" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Subject */}
          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tCommon("subject")}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={tFormSubjects("placeholder")} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {subjectOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Message */}
          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{tCommon("message")}</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Sua Mensagem..."
                    className="min-h-[120px] resize-none"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full bg-orange-500 hover:bg-orange-600"
            disabled={isPending}
          >
            {t("send")}
            {isPending && <Loader2 className="ml-2 size-4 animate-spin" />}
          </Button>
        </form>
      </Form>
    </article>
  );
};

export default ContactForm;
