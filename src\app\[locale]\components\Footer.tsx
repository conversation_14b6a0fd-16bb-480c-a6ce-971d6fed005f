import { cn } from "@/lib/utils";
import { getTranslations } from "next-intl/server";
import { Link } from "@/i18n/navigation";
import { legalLinks, navLinks, socialLinks } from "../links";
import LogoIcon from "@/assets/imgs/logo.svg";
import { buttonVariants } from "@/components/ui/button";

type Props = {
  className?: string;
};

const Footer = async ({ className }: Props) => {
  const t = await getTranslations("footer");
  const tNav = await getTranslations("navigation");

  const columns: Record<
    string,
    {
      title: string;
      items: {
        label: string;
        value: string;
        Icon: React.ComponentType<any> | null;
      }[];
      iconOnly?: boolean;
      external?: boolean;
    }
  > = {
    navigation: {
      title: t("navigation"),
      items: Object.values(navLinks).map((link) => ({
        label: tNav(link.key),
        value: link.href,
        Icon: link.icon,
      })),
    },
    social: {
      title: t("social"),
      iconOnly: true,
      external: true,
      items: socialLinks.map((link) => ({
        label: link.key,
        value: link.href,
        Icon: link.icon,
      })),
    },
    legal: {
      title: t("legal"),
      items: Object.values(legalLinks).map((link) => ({
        label: tNav(link.key),
        value: link.href,
        Icon: link.icon,
      })),
    },
  };

  return (
    <footer className={cn("bg-card", className)}>
      <section
        className={cn(
          "border-border @container mx-auto flex max-w-[90rem] gap-10 rounded-lg border p-10 max-lg:flex-col-reverse max-lg:items-center",
        )}
      >
        <article className="flex flex-1 flex-col gap-5">
          <Link href="/" className="w-fit">
            <LogoIcon className="h-10 w-auto" />
          </Link>
          <p className="text-muted-foreground max-w-lg text-sm">
            {t("copyright")}
          </p>
        </article>
        <article className="flex flex-1 gap-10 lg:justify-end @max-md:flex-wrap">
          {Object.entries(columns).map(([key, column], index) => (
            <div key={key} className="flex flex-col gap-5">
              <h6 className="text-base font-bold">{column.title}</h6>
              <ul
                className={cn(
                  "grid grid-cols-2 gap-x-5 gap-y-2.5",
                  index === 1 && "grid-cols-3",
                  index === 2 && "grid-cols-1",
                )}
              >
                {column.items.map((item) => {
                  const ItemContent =
                    column.iconOnly && item.Icon ? (
                      <item.Icon className="size-5" />
                    ) : (
                      item.label
                    );
                  return (
                    <li
                      key={item.label}
                      className={cn(
                        buttonVariants({ variant: "link" }),
                        "text-muted-foreground hocus:[&_svg_*]:stroke-primary justify-start text-left text-sm",
                      )}
                    >
                      {column.external ? (
                        <a
                          href={item.value}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {ItemContent}
                        </a>
                      ) : (
                        <Link href={item.value}>{ItemContent}</Link>
                      )}
                    </li>
                  );
                })}
              </ul>
            </div>
          ))}
        </article>
      </section>
    </footer>
  );
};

export default Footer;
