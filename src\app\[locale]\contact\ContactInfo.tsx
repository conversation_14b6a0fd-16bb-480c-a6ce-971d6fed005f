import { getTranslations } from "next-intl/server";
import { Phone, Mail, Clock, MapPinned } from "lucide-react";
import WhatsApp from "@/assets/imgs/icons/whatsapp.svg";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const ContactInfo = async ({ className }: Props) => {
  const t = await getTranslations("contact.info");
  const tCommon = await getTranslations("common");

  const contactItems = [
    {
      icon: MapPinned,
      label: tCommon("location"),
      value: t("location"),
      ariaLabel: "Our location",
    },
    {
      icon: Phone,
      label: tCommon("phone"),
      value: t("phone"),
      ariaLabel: "Phone number",
    },
    {
      icon: WhatsApp,
      label: tC<PERSON>mon("whatsapp"),
      value: t("whatsapp"),
      ariaLabel: "WhatsApp number",
    },
    {
      icon: Mail,
      label: tC<PERSON><PERSON>("email"),
      value: t("email"),
      ariaLabel: "Email address",
    },
    {
      icon: Clock,
      label: tC<PERSON>mon("businessHours"),
      value: t("businessHours"),
      ariaLabel: "Business hours",
    },
  ];

  return (
    <section className={cn("grid gap-5", className)}>
      {contactItems.map((item, index) => {
        const Icon = item.icon as React.ComponentType<any>;
        return (
          <article
            key={index}
            className="bg-neutral flex flex-col gap-2.5 rounded-lg border p-5"
            aria-label={item.ariaLabel}
          >
            <span className="text-foreground flex items-center gap-2.5">
              <Icon className="size-4" />
              <h6 className="text-sm font-bold">{item.label}</h6>
            </span>
            <p className="text-muted-foreground text-sm leading-relaxed">
              {item.value}
            </p>
          </article>
        );
      })}
    </section>
  );
};

export default ContactInfo;
