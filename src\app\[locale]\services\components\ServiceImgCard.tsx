"use client";

import { useQueryStates } from "nuqs";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { ServiceItemType } from "../data";
import { serviceSearchParams, serviceUrlKeys } from "../searchParams";

type Props = {
  item: ServiceItemType;
  className?: string;
};

const ServiceImgCard = ({ item, className }: Props) => {
  const [, setPreviewKey] = useQueryStates(serviceSearchParams, {
    urlKeys: serviceUrlKeys,
  });

  const t = useTranslations("services");

  const handleClick = () => {
    setPreviewKey({ selectedService: item.key });
  };

  const title = t(item.titleKey);
  const description = t(item.descriptionKey);

  return (
    <button
      onClick={handleClick}
      className={cn(
        "group focus-visible:border-primary relative flex aspect-[4/3] w-full cursor-pointer flex-col overflow-hidden rounded-lg focus-visible:border-2",
        className,
      )}
    >
      <Image
        src={item.images[0]}
        alt={title}
        fill
        className="group-hocus:scale-110 relative object-cover transition-transform duration-300"
      />

      <div className="group-hocus:opacity-100 absolute inset-0 flex flex-1 flex-col items-start justify-end bg-gradient-to-t from-black/90 to-black/10 p-5 text-left opacity-0 transition-opacity duration-300">
        <h5 className="text-neutral text-base font-semibold">{title}</h5>
        <p className="text-neutral/90 mt-1 line-clamp-2 text-sm">
          {description}
        </p>
      </div>
    </button>
  );
};

export default ServiceImgCard;
