{"name": "wa-2025", "version": "2025.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@next/third-parties": "^15.5.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@svgr/webpack": "^8.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.544.0", "next": "15.5.3", "next-intl": "^4.3.7", "next-themes": "^0.4.6", "nodemailer": "^7.0.6", "nuqs": "^2.6.0", "photoswipe": "^5.4.4", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.1.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.13", "@types/node": "^24.3.1", "@types/nodemailer": "^7.0.1", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "eslint": "^9.35.0", "eslint-config-next": "15.5.3", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.13", "tw-animate-css": "^1.3.8", "typescript": "^5.9.2"}}